"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";

interface NavigationProgressProps {
  color?: string;
  height?: number;
  speed?: number;
  showSpinner?: boolean;
  delay?: number;
  minDuration?: number;
  excludePaths?: string[];
  includeSearchParams?: boolean;
}

const NavigationProgress: React.FC<NavigationProgressProps> = ({
  color = "#4f46e5",
  height = 3,
  speed = 150,
  showSpinner = false,
  delay = 100,
  minDuration = 300,
  excludePaths = [],
  includeSearchParams = false,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [previousPath, setPreviousPath] = useState(pathname);
  const [showProgress, setShowProgress] = useState(false);

  // Refs to store timer IDs for cleanup
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const delayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const completeTimerRef = useRef<NodeJS.Timeout | null>(null);
  const minDurationTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Create full path including search params if needed
  const fullPath =
    includeSearchParams && searchParams.toString()
      ? `${pathname}?${searchParams.toString()}`
      : pathname;

  // Cleanup function for all timers
  const cleanupTimers = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (delayTimerRef.current) {
      clearTimeout(delayTimerRef.current);
      delayTimerRef.current = null;
    }
    if (completeTimerRef.current) {
      clearTimeout(completeTimerRef.current);
      completeTimerRef.current = null;
    }
    if (minDurationTimerRef.current) {
      clearTimeout(minDurationTimerRef.current);
      minDurationTimerRef.current = null;
    }
  };

  useEffect(() => {
    // Check if this path should be excluded from loading
    const shouldExclude = excludePaths.some((excludePath) =>
      pathname.startsWith(excludePath)
    );

    if (shouldExclude) return;

    // Only trigger loading when path actually changes
    if (fullPath !== previousPath) {
      // Cleanup any existing timers
      cleanupTimers();

      setIsLoading(true);
      setProgress(0);
      setShowProgress(false);

      // Start progress after delay to avoid flash for fast navigations
      delayTimerRef.current = setTimeout(() => {
        setShowProgress(true);

        // Start progress animation
        progressIntervalRef.current = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 90) {
              if (progressIntervalRef.current) {
                clearInterval(progressIntervalRef.current);
                progressIntervalRef.current = null;
              }
              return prev;
            }
            // More realistic progress increments
            const increment = Math.random() * 8 + 2; // 2-10% increments
            return Math.min(prev + increment, 90);
          });
        }, speed);
      }, delay);

      // Complete the progress after minimum duration
      minDurationTimerRef.current = setTimeout(() => {
        setProgress(100);

        // Hide progress bar after completion animation
        completeTimerRef.current = setTimeout(() => {
          setIsLoading(false);
          setShowProgress(false);
          setProgress(0);
        }, 200);
      }, minDuration);

      setPreviousPath(fullPath);

      return cleanupTimers;
    }
  }, [
    pathname,
    searchParams,
    previousPath,
    speed,
    delay,
    minDuration,
    excludePaths,
    includeSearchParams,
    fullPath,
  ]);

  // Cleanup timers on unmount
  useEffect(() => {
    return cleanupTimers;
  }, []);

  if (!isLoading || !showProgress) return null;

  return (
    <>
      {/* Progress bar */}
      <div
        className="fixed top-0 left-0 z-50 transition-all duration-200 ease-out"
        style={{
          height: `${height}px`,
          width: `${progress}%`,
          backgroundColor: color,
          boxShadow: `0 0 10px ${color}`,
          opacity: showProgress ? 1 : 0,
        }}
      />

      {/* Optional spinner */}
      {showSpinner && showProgress && (
        <div className="fixed top-4 right-4 z-50">
          <div
            className="animate-spin rounded-full border-2 border-t-transparent w-6 h-6"
            style={{
              borderColor: `${color} transparent transparent transparent`,
            }}
          />
        </div>
      )}
    </>
  );
};

export default NavigationProgress;
