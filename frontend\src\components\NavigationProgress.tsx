"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { usePathname, useSearchParams } from "next/navigation";

interface NavigationProgressProps {
  color?: string;
  height?: number;
  speed?: number;
  showSpinner?: boolean;
  delay?: number;
  minDuration?: number;
  excludePaths?: string[];
  includeSearchParams?: boolean;
}

const NavigationProgress: React.FC<NavigationProgressProps> = ({
  color = "#4f46e5",
  height = 4,
  speed = 100,
  showSpinner = false,
  delay = 50,
  minDuration = 500,
  excludePaths = [],
  includeSearchParams = false,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showProgress, setShowProgress] = useState(false);

  // Refs to store timer IDs for cleanup
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const delayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const completeTimerRef = useRef<NodeJS.Timeout | null>(null);
  const minDurationTimerRef = useRef<NodeJS.Timeout | null>(null);
  const previousPathRef = useRef(pathname);

  // Cleanup function for all timers
  const cleanupTimers = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (delayTimerRef.current) {
      clearTimeout(delayTimerRef.current);
      delayTimerRef.current = null;
    }
    if (completeTimerRef.current) {
      clearTimeout(completeTimerRef.current);
      completeTimerRef.current = null;
    }
    if (minDurationTimerRef.current) {
      clearTimeout(minDurationTimerRef.current);
      minDurationTimerRef.current = null;
    }
  }, []);

  // Start loading animation
  const startLoading = useCallback(() => {
    console.log("🚀 Starting navigation loading animation");
    cleanupTimers();

    setIsLoading(true);
    setProgress(0);
    setShowProgress(false);

    // Start progress after delay to avoid flash for fast navigations
    delayTimerRef.current = setTimeout(() => {
      setShowProgress(true);
      console.log("📊 Showing progress bar");

      // Start progress animation
      progressIntervalRef.current = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            if (progressIntervalRef.current) {
              clearInterval(progressIntervalRef.current);
              progressIntervalRef.current = null;
            }
            return prev;
          }
          // More realistic progress increments
          const increment = Math.random() * 10 + 5; // 5-15% increments
          return Math.min(prev + increment, 90);
        });
      }, speed);
    }, delay);

    // Complete the progress after minimum duration
    minDurationTimerRef.current = setTimeout(() => {
      console.log("✅ Completing navigation loading");
      setProgress(100);

      // Hide progress bar after completion animation
      completeTimerRef.current = setTimeout(() => {
        setIsLoading(false);
        setShowProgress(false);
        setProgress(0);
        console.log("🏁 Navigation loading finished");
      }, 300);
    }, minDuration);
  }, [cleanupTimers, delay, speed, minDuration]);

  useEffect(() => {
    // Create full path including search params if needed
    const currentPath =
      includeSearchParams && searchParams.toString()
        ? `${pathname}?${searchParams.toString()}`
        : pathname;

    // Check if this path should be excluded from loading
    const shouldExclude = excludePaths.some((excludePath) =>
      pathname.startsWith(excludePath)
    );

    if (shouldExclude) {
      console.log("⏭️ Skipping loading for excluded path:", pathname);
      return;
    }

    // Only trigger loading when path actually changes
    if (currentPath !== previousPathRef.current) {
      console.log(
        "🔄 Path changed from",
        previousPathRef.current,
        "to",
        currentPath
      );
      startLoading();
      previousPathRef.current = currentPath;
    }
  }, [pathname, searchParams, excludePaths, includeSearchParams, startLoading]);

  // Cleanup timers on unmount
  useEffect(() => {
    return cleanupTimers;
  }, [cleanupTimers]);

  if (!showProgress) return null;

  return (
    <>
      {/* Progress bar */}
      <div
        className="fixed top-0 left-0 z-50 transition-all duration-300 ease-out"
        style={{
          height: `${height}px`,
          width: `${progress}%`,
          backgroundColor: color,
          boxShadow: `0 0 15px ${color}40, 0 0 5px ${color}`,
          opacity: showProgress ? 1 : 0,
          borderRadius: "0 2px 2px 0",
        }}
      />

      {/* Optional spinner */}
      {showSpinner && showProgress && (
        <div className="fixed top-4 right-4 z-50 bg-white rounded-full p-2 shadow-lg">
          <div
            className="animate-spin rounded-full border-2 border-t-transparent w-6 h-6"
            style={{
              borderColor: `${color} transparent transparent transparent`,
            }}
          />
        </div>
      )}
    </>
  );
};

export default NavigationProgress;
