{"version": 3, "sources": [], "sections": [{"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/Layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { AuthService, CartService } from '../services';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [cartItemCount, setCartItemCount] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check authentication status\n    setIsAuthenticated(AuthService.isAuthenticated());\n    \n    // Get cart item count\n    setCartItemCount(CartService.getItemCount());\n    \n    // Add event listener for cart updates\n    const handleStorageChange = () => {\n      setCartItemCount(CartService.getItemCount());\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    await AuthService.logout();\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <Link to=\"/\" className=\"text-2xl font-bold text-indigo-600\">\n                  EcommerceApp\n                </Link>\n              </div>\n              <nav className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                <Link\n                  to=\"/\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Home\n                </Link>\n                <Link\n                  to=\"/products\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Products\n                </Link>\n                <Link\n                  to=\"/categories\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Categories\n                </Link>\n              </nav>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\">\n              <Link to=\"/cart\" className=\"relative p-1 rounded-full text-gray-400 hover:text-gray-500\">\n                <ShoppingCartIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                {cartItemCount > 0 && (\n                  <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full\">\n                    {cartItemCount}\n                  </span>\n                )}\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"p-1 rounded-full text-gray-400 hover:text-gray-500\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n            <div className=\"-mr-2 flex items-center sm:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {isMenuOpen ? (\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                ) : (\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1\">\n              <Link\n                to=\"/\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                to=\"/products\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Products\n              </Link>\n              <Link\n                to=\"/categories\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Categories\n              </Link>\n              <Link\n                to=\"/cart\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Cart ({cartItemCount})\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main content */}\n      <main className=\"flex-grow\">\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n          <p className=\"text-center text-gray-500 text-sm\">\n            &copy; {new Date().getFullYear()} EcommerceApp. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,8BAA8B;YAC9B,mBAAmB,+KAAA,CAAA,cAAW,CAAC,eAAe;YAE9C,sBAAsB;YACtB,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;YAEzC,sCAAsC;YACtC,MAAM;wDAAsB;oBAC1B,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;gBAC3C;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,+KAAA,CAAA,cAAW,CAAC,MAAM;QACxB,mBAAmB;QACnB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+KAAA,CAAA,OAAI;gDAAC,IAAG;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+KAAA,CAAA,OAAI;4CAAC,IAAG;4CAAQ,WAAU;;8DACzB,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDACjD,gBAAgB,mBACf,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;wCAIN,gCACC;;8DACE,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DAEV,cAAA,6LAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;8DAE5C,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;yEAKH;;8DACE,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;8CAMP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,2BACC,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1D,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;;wCAC9B;wCACQ;wCAAc;;;;;;;gCAEtB,gCACC;;sDACE,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;iEAKH;;sDACE,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAoC;4BACvC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;GApNM;;QAIa,+KAAA,CAAA,cAAW;;;KAJxB;uCAsNS", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Product, CartService } from '../services';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const handleAddToCart = () => {\n    CartService.addToCart(product, 1);\n    // Trigger storage event to update cart count in Layout\n    window.dispatchEvent(new Event('storage'));\n  };\n\n  return (\n    <div className=\"group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden\">\n      <div className=\"aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 h-48\">\n        {product.image ? (\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"flex flex-col space-y-2 p-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">\n          <Link href={`/products/${product.slug}`} className=\"block\">\n            <span aria-hidden=\"true\" className=\"absolute inset-0\" />\n            {product.name}\n          </Link>\n        </h3>\n        <p className=\"text-sm text-gray-500 line-clamp-2\">{product.description}</p>\n        <div className=\"flex justify-between items-center\">\n          <p className=\"text-lg font-medium text-gray-900\">${Number(product.price).toFixed(2)}</p>\n          {product.quantity > 0 ? (\n            <span className=\"text-sm text-green-600\">In Stock</span>\n          ) : (\n            <span className=\"text-sm text-red-600\">Out of Stock</span>\n          )}\n        </div>\n        <button\n          onClick={handleAddToCart}\n          disabled={product.quantity <= 0}\n          className={`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${\n            product.quantity > 0\n              ? 'bg-indigo-600 hover:bg-indigo-700'\n              : 'bg-gray-400 cursor-not-allowed'\n          }`}\n        >\n          Add to Cart\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAMA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,MAAM,kBAAkB;QACtB,+KAAA,CAAA,cAAW,CAAC,SAAS,CAAC,SAAS;QAC/B,uDAAuD;QACvD,OAAO,aAAa,CAAC,IAAI,MAAM;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,KAAK,iBACZ,6LAAC;oBACC,KAAK,QAAQ,KAAK;oBAClB,KAAK,QAAQ,IAAI;oBACjB,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;4BAAE,WAAU;;8CACjD,6LAAC;oCAAK,eAAY;oCAAO,WAAU;;;;;;gCAClC,QAAQ,IAAI;;;;;;;;;;;;kCAGjB,6LAAC;wBAAE,WAAU;kCAAsC,QAAQ,WAAW;;;;;;kCACtE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAoC;oCAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC;;;;;;;4BAChF,QAAQ,QAAQ,GAAG,kBAClB,6LAAC;gCAAK,WAAU;0CAAyB;;;;;qDAEzC,6LAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAG3C,6LAAC;wBACC,SAAS;wBACT,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,WAAW,CAAC,kIAAkI,EAC5I,QAAQ,QAAQ,GAAG,IACf,sCACA,kCACJ;kCACH;;;;;;;;;;;;;;;;;;AAMT;KApDM;uCAsDS", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Category } from '../services';\n\ninterface CategoryCardProps {\n  category: Category;\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link\n      href={`/categories/${category.slug}`}\n      className=\"group block bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\"\n    >\n      <div className=\"aspect-w-3 aspect-h-2 bg-gray-200 group-hover:opacity-75 h-40\">\n        {category.image ? (\n          <img\n            src={category.image}\n            alt={category.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{category.name}</h3>\n        {category.description && (\n          <p className=\"mt-1 text-sm text-gray-500 line-clamp-2\">{category.description}</p>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;QACpC,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,iBACb,6LAAC;oBACC,KAAK,SAAS,KAAK;oBACnB,KAAK,SAAS,IAAI;oBAClB,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC,SAAS,IAAI;;;;;;oBAC/D,SAAS,WAAW,kBACnB,6LAAC;wBAAE,WAAU;kCAA2C,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAKtF;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/StripePaymentForm.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useStripe, useElements, CardElement } from \"@stripe/react-stripe-js\";\nimport { StripeCardElementOptions } from \"@stripe/stripe-js\";\n\ninterface StripePaymentFormProps {\n  onSuccess: (paymentMethodId: string) => void;\n  onError: (error: string) => void;\n  loading: boolean;\n  setLoading: (loading: boolean) => void;\n}\n\nconst cardElementOptions: StripeCardElementOptions = {\n  style: {\n    base: {\n      fontSize: \"16px\",\n      color: \"#424770\",\n      \"::placeholder\": {\n        color: \"#aab7c4\",\n      },\n    },\n    invalid: {\n      color: \"#9e2146\",\n    },\n  },\n};\n\nconst StripePaymentForm: React.FC<StripePaymentFormProps> = ({\n  onSuccess,\n  onError,\n  loading,\n  setLoading,\n}) => {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [cardError, setCardError] = useState<string | null>(null);\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    const cardElement = elements.getElement(CardElement);\n\n    if (!cardElement) {\n      return;\n    }\n\n    setLoading(true);\n    setCardError(null);\n\n    try {\n      // Create payment method\n      const { error, paymentMethod } = await stripe.createPaymentMethod({\n        type: \"card\",\n        card: cardElement,\n      });\n\n      if (error) {\n        setCardError(error.message || \"An error occurred during payment\");\n        onError(error.message || \"An error occurred during payment\");\n      } else if (paymentMethod) {\n        onSuccess(paymentMethod.id);\n      }\n    } catch {\n      setCardError(\"Payment failed. Please try again.\");\n      onError(\"Payment failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCardChange = (event: { error?: { message: string } }) => {\n    if (event.error) {\n      setCardError(event.error.message);\n    } else {\n      setCardError(null);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Card Information\n        </label>\n        <div className=\"border border-gray-300 rounded-md p-3 bg-white\">\n          <CardElement\n            options={cardElementOptions}\n            onChange={handleCardChange}\n          />\n        </div>\n        {cardError && <p className=\"mt-1 text-sm text-red-600\">{cardError}</p>}\n      </div>\n\n      <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-blue-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-blue-700\">\n              This is a test environment. Use test card number 4242 4242 4242\n              4242 with any future expiry date and any 3-digit CVC.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={!stripe || loading}\n        className={`w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${\n          !stripe || loading\n            ? \"bg-gray-400 cursor-not-allowed\"\n            : \"bg-indigo-600 hover:bg-indigo-700 cursor-pointer\"\n        }`}\n      >\n        {loading ? (\n          <>\n            <div className=\"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2\"></div>\n            Processing Payment...\n          </>\n        ) : (\n          \"Pay Now\"\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default StripePaymentForm;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,qBAA+C;IACnD,OAAO;QACL,MAAM;YACJ,UAAU;YACV,OAAO;YACP,iBAAiB;gBACf,OAAO;YACT;QACF;QACA,SAAS;YACP,OAAO;QACT;IACF;AACF;AAEA,MAAM,oBAAsD,CAAC,EAC3D,SAAS,EACT,OAAO,EACP,OAAO,EACP,UAAU,EACX;;IACC,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,cAAc,SAAS,UAAU,CAAC,sLAAA,CAAA,cAAW;QAEnD,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,wBAAwB;YACxB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,OAAO,mBAAmB,CAAC;gBAChE,MAAM;gBACN,MAAM;YACR;YAEA,IAAI,OAAO;gBACT,aAAa,MAAM,OAAO,IAAI;gBAC9B,QAAQ,MAAM,OAAO,IAAI;YAC3B,OAAO,IAAI,eAAe;gBACxB,UAAU,cAAc,EAAE;YAC5B;QACF,EAAE,OAAM;YACN,aAAa;YACb,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,KAAK,EAAE;YACf,aAAa,MAAM,KAAK,CAAC,OAAO;QAClC,OAAO;YACL,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,sLAAA,CAAA,cAAW;4BACV,SAAS;4BACT,UAAU;;;;;;;;;;;oBAGb,2BAAa,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEL,cAAA,6LAAC;oCACC,UAAS;oCACT,GAAE;oCACF,UAAS;;;;;;;;;;;;;;;;sCAIf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU;gBACrB,WAAW,CAAC,+HAA+H,EACzI,CAAC,UAAU,UACP,mCACA,oDACJ;0BAED,wBACC;;sCACE,6LAAC;4BAAI,WAAU;;;;;;wBAAkF;;mCAInG;;;;;;;;;;;;AAKV;GAlHM;;QAMW,sLAAA,CAAA,YAAS;QACP,sLAAA,CAAA,cAAW;;;KAPxB;uCAoHS", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n  color?: \"primary\" | \"secondary\" | \"white\";\n  text?: string;\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = \"md\",\n  color = \"primary\",\n  text,\n  className = \"\",\n}) => {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-8 w-8\",\n    lg: \"h-12 w-12\",\n    xl: \"h-16 w-16\",\n  };\n\n  const colorClasses = {\n    primary: \"border-indigo-500\",\n    secondary: \"border-gray-500\",\n    white: \"border-white\",\n  };\n\n  const textSizeClasses = {\n    sm: \"text-sm\",\n    md: \"text-base\",\n    lg: \"text-lg\",\n    xl: \"text-xl\",\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <div\n        className={`animate-spin rounded-full border-t-2 border-b-2 ${sizeClasses[size]} ${colorClasses[color]}`}\n      />\n      {text && (\n        <p className={`mt-2 text-gray-600 ${textSizeClasses[size]}`}>{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,iBAAgD,CAAC,EACrD,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,IAAI,EACJ,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC;gBACC,WAAW,CAAC,gDAAgD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,EAAE;;;;;;YAEzG,sBACC,6LAAC;gBAAE,WAAW,CAAC,mBAAmB,EAAE,eAAe,CAAC,KAAK,EAAE;0BAAG;;;;;;;;;;;;AAItE;KApCM;uCAsCS", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/SkeletonLoader.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface SkeletonLineProps {\n  width?: string;\n  height?: string;\n  className?: string;\n}\n\ninterface SkeletonTextProps {\n  lines?: number;\n  className?: string;\n}\n\ninterface SkeletonImageProps {\n  width?: string;\n  height?: string;\n  className?: string;\n}\n\ninterface SkeletonCardProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\ninterface SkeletonGridProps {\n  columns?: number;\n  rows?: number;\n  gap?: string;\n  className?: string;\n}\n\n// Base skeleton animation class\nconst skeletonBase = \"animate-pulse bg-gray-200 rounded\";\n\n// Basic line skeleton component\nexport const SkeletonLine: React.FC<SkeletonLineProps> = ({ \n  width = \"100%\", \n  height = \"1rem\", \n  className = \"\" \n}) => {\n  return (\n    <div \n      className={`${skeletonBase} ${className}`}\n      style={{ width, height }}\n    />\n  );\n};\n\n// Multi-line text skeleton\nexport const SkeletonText: React.FC<SkeletonTextProps> = ({ \n  lines = 3, \n  className = \"\" \n}) => {\n  return (\n    <div className={`space-y-2 ${className}`}>\n      {Array.from({ length: lines }).map((_, index) => (\n        <SkeletonLine \n          key={index}\n          width={index === lines - 1 ? \"75%\" : \"100%\"}\n          height=\"1rem\"\n        />\n      ))}\n    </div>\n  );\n};\n\n// Image placeholder skeleton\nexport const SkeletonImage: React.FC<SkeletonImageProps> = ({ \n  width = \"100%\", \n  height = \"12rem\", \n  className = \"\" \n}) => {\n  return (\n    <div \n      className={`${skeletonBase} flex items-center justify-center ${className}`}\n      style={{ width, height }}\n    >\n      <svg \n        className=\"w-8 h-8 text-gray-300\" \n        fill=\"currentColor\" \n        viewBox=\"0 0 20 20\"\n      >\n        <path \n          fillRule=\"evenodd\" \n          d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" \n          clipRule=\"evenodd\" \n        />\n      </svg>\n    </div>\n  );\n};\n\n// Card container skeleton\nexport const SkeletonCard: React.FC<SkeletonCardProps> = ({ \n  className = \"\", \n  children \n}) => {\n  return (\n    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Grid layout skeleton\nexport const SkeletonGrid: React.FC<SkeletonGridProps> = ({ \n  columns = 4, \n  rows = 3, \n  gap = \"gap-6\", \n  className = \"\" \n}) => {\n  const gridCols = {\n    1: \"grid-cols-1\",\n    2: \"grid-cols-2\", \n    3: \"grid-cols-3\",\n    4: \"grid-cols-4\",\n    5: \"grid-cols-5\",\n    6: \"grid-cols-6\"\n  };\n\n  return (\n    <div className={`grid ${gridCols[columns as keyof typeof gridCols] || 'grid-cols-4'} ${gap} ${className}`}>\n      {Array.from({ length: columns * rows }).map((_, index) => (\n        <SkeletonCard key={index}>\n          <SkeletonImage height=\"8rem\" className=\"mb-4\" />\n          <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n          <SkeletonText lines={2} />\n        </SkeletonCard>\n      ))}\n    </div>\n  );\n};\n\n// Specialized skeleton components for specific use cases\n\n// Product card skeleton\nexport const ProductCardSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <SkeletonCard className={className}>\n      <SkeletonImage height=\"12rem\" className=\"mb-4\" />\n      <SkeletonLine height=\"1.5rem\" className=\"mb-2\" />\n      <SkeletonText lines={2} className=\"mb-3\" />\n      <div className=\"flex justify-between items-center\">\n        <SkeletonLine width=\"4rem\" height=\"1.25rem\" />\n        <SkeletonLine width=\"5rem\" height=\"2rem\" />\n      </div>\n    </SkeletonCard>\n  );\n};\n\n// Category card skeleton\nexport const CategoryCardSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <SkeletonCard className={className}>\n      <SkeletonImage height=\"8rem\" className=\"mb-3\" />\n      <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n      <SkeletonLine width=\"60%\" height=\"1rem\" />\n    </SkeletonCard>\n  );\n};\n\n// Order item skeleton\nexport const OrderItemSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <div className={`flex items-center space-x-4 p-4 border-b border-gray-200 ${className}`}>\n      <SkeletonImage width=\"4rem\" height=\"4rem\" />\n      <div className=\"flex-1\">\n        <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n        <SkeletonLine width=\"40%\" height=\"1rem\" />\n      </div>\n      <div className=\"text-right\">\n        <SkeletonLine width=\"3rem\" height=\"1.25rem\" className=\"mb-1\" />\n        <SkeletonLine width=\"2rem\" height=\"1rem\" />\n      </div>\n    </div>\n  );\n};\n\n// User profile skeleton\nexport const UserProfileSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <SkeletonCard className={className}>\n      <div className=\"flex items-center space-x-4 mb-6\">\n        <div className={`${skeletonBase} rounded-full w-16 h-16`} />\n        <div className=\"flex-1\">\n          <SkeletonLine height=\"1.5rem\" className=\"mb-2\" />\n          <SkeletonLine width=\"60%\" height=\"1rem\" />\n        </div>\n      </div>\n      <div className=\"space-y-4\">\n        <div>\n          <SkeletonLine width=\"25%\" height=\"1rem\" className=\"mb-2\" />\n          <SkeletonLine height=\"2.5rem\" />\n        </div>\n        <div>\n          <SkeletonLine width=\"25%\" height=\"1rem\" className=\"mb-2\" />\n          <SkeletonLine height=\"2.5rem\" />\n        </div>\n        <div>\n          <SkeletonLine width=\"25%\" height=\"1rem\" className=\"mb-2\" />\n          <SkeletonLine height=\"2.5rem\" />\n        </div>\n      </div>\n    </SkeletonCard>\n  );\n};\n\n// Table skeleton\nexport const TableSkeleton: React.FC<{ \n  rows?: number; \n  columns?: number; \n  className?: string \n}> = ({ \n  rows = 5, \n  columns = 4, \n  className = \"\" \n}) => {\n  return (\n    <div className={`overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg ${className}`}>\n      <table className=\"min-w-full divide-y divide-gray-300\">\n        <thead className=\"bg-gray-50\">\n          <tr>\n            {Array.from({ length: columns }).map((_, index) => (\n              <th key={index} className=\"px-6 py-3\">\n                <SkeletonLine height=\"1rem\" />\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody className=\"divide-y divide-gray-200 bg-white\">\n          {Array.from({ length: rows }).map((_, rowIndex) => (\n            <tr key={rowIndex}>\n              {Array.from({ length: columns }).map((_, colIndex) => (\n                <td key={colIndex} className=\"px-6 py-4\">\n                  <SkeletonLine height=\"1rem\" />\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n};\n\n// List skeleton\nexport const ListSkeleton: React.FC<{ \n  items?: number; \n  className?: string \n}> = ({ \n  items = 5, \n  className = \"\" \n}) => {\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {Array.from({ length: items }).map((_, index) => (\n        <div key={index} className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\">\n          <SkeletonImage width=\"3rem\" height=\"3rem\" />\n          <div className=\"flex-1\">\n            <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n            <SkeletonLine width=\"70%\" height=\"1rem\" />\n          </div>\n          <SkeletonLine width=\"4rem\" height=\"1rem\" />\n        </div>\n      ))}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AA+BA,gCAAgC;AAChC,MAAM,eAAe;AAGd,MAAM,eAA4C,CAAC,EACxD,QAAQ,MAAM,EACd,SAAS,MAAM,EACf,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QACC,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;QACzC,OAAO;YAAE;YAAO;QAAO;;;;;;AAG7B;KAXa;AAcN,MAAM,eAA4C,CAAC,EACxD,QAAQ,CAAC,EACT,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;kBACrC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAEC,OAAO,UAAU,QAAQ,IAAI,QAAQ;gBACrC,QAAO;eAFF;;;;;;;;;;AAOf;MAfa;AAkBN,MAAM,gBAA8C,CAAC,EAC1D,QAAQ,MAAM,EACd,SAAS,OAAO,EAChB,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QACC,WAAW,GAAG,aAAa,kCAAkC,EAAE,WAAW;QAC1E,OAAO;YAAE;YAAO;QAAO;kBAEvB,cAAA,6LAAC;YACC,WAAU;YACV,MAAK;YACL,SAAQ;sBAER,cAAA,6LAAC;gBACC,UAAS;gBACT,GAAE;gBACF,UAAS;;;;;;;;;;;;;;;;AAKnB;MAvBa;AA0BN,MAAM,eAA4C,CAAC,EACxD,YAAY,EAAE,EACd,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;kBAC7D;;;;;;AAGP;MATa;AAYN,MAAM,eAA4C,CAAC,EACxD,UAAU,CAAC,EACX,OAAO,CAAC,EACR,MAAM,OAAO,EACb,YAAY,EAAE,EACf;IACC,MAAM,WAAW;QACf,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,QAAiC,IAAI,cAAc,CAAC,EAAE,IAAI,CAAC,EAAE,WAAW;kBACtG,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC9C,6LAAC;;kCACC,6LAAC;wBAAc,QAAO;wBAAO,WAAU;;;;;;kCACvC,6LAAC;wBAAa,QAAO;wBAAU,WAAU;;;;;;kCACzC,6LAAC;wBAAa,OAAO;;;;;;;eAHJ;;;;;;;;;;AAQ3B;MA1Ba;AA+BN,MAAM,sBAAwD,CAAC,EACpE,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAa,WAAW;;0BACvB,6LAAC;gBAAc,QAAO;gBAAQ,WAAU;;;;;;0BACxC,6LAAC;gBAAa,QAAO;gBAAS,WAAU;;;;;;0BACxC,6LAAC;gBAAa,OAAO;gBAAG,WAAU;;;;;;0BAClC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;kCAClC,6LAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;;;;;;;;;;;;;AAI1C;MAda;AAiBN,MAAM,uBAAyD,CAAC,EACrE,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAa,WAAW;;0BACvB,6LAAC;gBAAc,QAAO;gBAAO,WAAU;;;;;;0BACvC,6LAAC;gBAAa,QAAO;gBAAU,WAAU;;;;;;0BACzC,6LAAC;gBAAa,OAAM;gBAAM,QAAO;;;;;;;;;;;;AAGvC;MAVa;AAaN,MAAM,oBAAsD,CAAC,EAClE,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;;0BACrF,6LAAC;gBAAc,OAAM;gBAAO,QAAO;;;;;;0BACnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAa,QAAO;wBAAU,WAAU;;;;;;kCACzC,6LAAC;wBAAa,OAAM;wBAAM,QAAO;;;;;;;;;;;;0BAEnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAa,OAAM;wBAAO,QAAO;wBAAU,WAAU;;;;;;kCACtD,6LAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;;;;;;;;;;;;;AAI1C;MAhBa;AAmBN,MAAM,sBAAwD,CAAC,EACpE,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAa,WAAW;;0BACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,GAAG,aAAa,uBAAuB,CAAC;;;;;;kCACxD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAa,QAAO;gCAAS,WAAU;;;;;;0CACxC,6LAAC;gCAAa,OAAM;gCAAM,QAAO;;;;;;;;;;;;;;;;;;0BAGrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAa,OAAM;gCAAM,QAAO;gCAAO,WAAU;;;;;;0CAClD,6LAAC;gCAAa,QAAO;;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAa,OAAM;gCAAM,QAAO;gCAAO,WAAU;;;;;;0CAClD,6LAAC;gCAAa,QAAO;;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAa,OAAM;gCAAM,QAAO;gCAAO,WAAU;;;;;;0CAClD,6LAAC;gCAAa,QAAO;;;;;;;;;;;;;;;;;;;;;;;;AAK/B;MA5Ba;AA+BN,MAAM,gBAIR,CAAC,EACJ,OAAO,CAAC,EACR,UAAU,CAAC,EACX,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,sEAAsE,EAAE,WAAW;kBAClG,cAAA,6LAAC;YAAM,WAAU;;8BACf,6LAAC;oBAAM,WAAU;8BACf,cAAA,6LAAC;kCACE,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,sBACvC,6LAAC;gCAAe,WAAU;0CACxB,cAAA,6LAAC;oCAAa,QAAO;;;;;;+BADd;;;;;;;;;;;;;;;8BAMf,6LAAC;oBAAM,WAAU;8BACd,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,6LAAC;sCACE,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,6LAAC;oCAAkB,WAAU;8CAC3B,cAAA,6LAAC;wCAAa,QAAO;;;;;;mCADd;;;;;2BAFJ;;;;;;;;;;;;;;;;;;;;;AAYrB;MAnCa;AAsCN,MAAM,eAGR,CAAC,EACJ,QAAQ,CAAC,EACT,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;kBACrC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAAgB,WAAU;;kCACzB,6LAAC;wBAAc,OAAM;wBAAO,QAAO;;;;;;kCACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAa,QAAO;gCAAU,WAAU;;;;;;0CACzC,6LAAC;gCAAa,OAAM;gCAAM,QAAO;;;;;;;;;;;;kCAEnC,6LAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;;eAN1B;;;;;;;;;;AAWlB;OArBa", "debugId": null}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/index.ts"], "sourcesContent": ["export { default as Layout } from './Layout';\nexport { default as ProductCard } from './ProductCard';\nexport { default as CategoryCard } from './CategoryCard';\nexport { default as VerificationNotice } from './VerificationNotice';\nexport { default as CsrfToken } from './CsrfToken';\nexport { default as StripePaymentForm } from './StripePaymentForm';\nexport { default as LoadingSpinner } from './LoadingSpinner';\n\n// Skeleton loading components\nexport {\n  SkeletonLine,\n  SkeletonText,\n  SkeletonImage,\n  SkeletonCard,\n  SkeletonGrid,\n  ProductCardSkeleton,\n  CategoryCardSkeleton,\n  OrderItemSkeleton,\n  UserProfileSkeleton,\n  TableSkeleton,\n  ListSkeleton,\n} from './SkeletonLoader';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,8BAA8B;AAC9B", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/categories/%5Bslug%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport { CategoryService, ProductService, Category, Product } from '../../../services';\nimport { ProductCard } from '../../../components';\n\nexport default function CategoryDetailPage() {\n  const params = useParams();\n  const slug = params.slug as string;\n\n  const [category, setCategory] = useState<Category | null>(null);\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchCategoryAndProducts = async () => {\n      try {\n        setLoading(true);\n\n        // Get category by slug\n        const foundCategory = await CategoryService.getCategoryBySlug(slug);\n        setCategory(foundCategory);\n\n        // Get all products\n        const allProducts = await ProductService.getAllProducts();\n\n        // Filter products by category\n        const categoryProducts = allProducts.filter(\n          product => product.category_id === foundCategory.id\n        );\n\n        setProducts(categoryProducts);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching category and products:', err);\n        setError('Failed to load category. Please try again later.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (slug) {\n      fetchCategoryAndProducts();\n    }\n  }, [slug]);\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500\"></div>\n      </div>\n    );\n  }\n\n  if (error || !category) {\n    return (\n      <div className=\"bg-red-50 border-l-4 border-red-400 p-4 my-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-red-700\">{error || 'Category not found'}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl\">{category.name}</h1>\n            {category.description && (\n              <p className=\"mt-4 max-w-2xl mx-auto text-xl text-gray-500\">{category.description}</p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Products in this category</h2>\n\n        {products.length > 0 ? (\n          <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4\">\n            {products.map((product) => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </div>\n        ) : (\n          <p className=\"text-gray-500\">No products available in this category.</p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,OAAO,IAAI;IAExB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;yEAA2B;oBAC/B,IAAI;wBACF,WAAW;wBAEX,uBAAuB;wBACvB,MAAM,gBAAgB,MAAM,uLAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;wBAC9D,YAAY;wBAEZ,mBAAmB;wBACnB,MAAM,cAAc,MAAM,qLAAA,CAAA,iBAAc,CAAC,cAAc;wBAEvD,8BAA8B;wBAC9B,MAAM,mBAAmB,YAAY,MAAM;sGACzC,CAAA,UAAW,QAAQ,WAAW,KAAK,cAAc,EAAE;;wBAGrD,YAAY;wBACZ,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,yCAAyC;wBACvD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,MAAM;gBACR;YACF;QACF;uCAAG;QAAC;KAAK;IAET,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAuB,SAAQ;4BAAY,MAAK;sCAC7D,cAAA,6LAAC;gCAAK,UAAS;gCAAU,GAAE;gCAA0N,UAAS;;;;;;;;;;;;;;;;kCAGlQ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB,SAAS;;;;;;;;;;;;;;;;;;;;;;IAKxD;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoE,SAAS,IAAI;;;;;;4BAC9F,SAAS,WAAW,kBACnB,6LAAC;gCAAE,WAAU;0CAAgD,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;0BAMzF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAErD,SAAS,MAAM,GAAG,kBACjB,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8KAAA,CAAA,cAAW;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;6CAIhC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;GA9FwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}