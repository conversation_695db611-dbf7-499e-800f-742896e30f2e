{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/Layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { AuthService, CartService } from '../services';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [cartItemCount, setCartItemCount] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check authentication status\n    setIsAuthenticated(AuthService.isAuthenticated());\n    \n    // Get cart item count\n    setCartItemCount(CartService.getItemCount());\n    \n    // Add event listener for cart updates\n    const handleStorageChange = () => {\n      setCartItemCount(CartService.getItemCount());\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    await AuthService.logout();\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <Link to=\"/\" className=\"text-2xl font-bold text-indigo-600\">\n                  EcommerceApp\n                </Link>\n              </div>\n              <nav className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                <Link\n                  to=\"/\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Home\n                </Link>\n                <Link\n                  to=\"/products\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Products\n                </Link>\n                <Link\n                  to=\"/categories\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Categories\n                </Link>\n              </nav>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\">\n              <Link to=\"/cart\" className=\"relative p-1 rounded-full text-gray-400 hover:text-gray-500\">\n                <ShoppingCartIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                {cartItemCount > 0 && (\n                  <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full\">\n                    {cartItemCount}\n                  </span>\n                )}\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"p-1 rounded-full text-gray-400 hover:text-gray-500\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n            <div className=\"-mr-2 flex items-center sm:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {isMenuOpen ? (\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                ) : (\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1\">\n              <Link\n                to=\"/\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                to=\"/products\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Products\n              </Link>\n              <Link\n                to=\"/categories\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Categories\n              </Link>\n              <Link\n                to=\"/cart\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Cart ({cartItemCount})\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main content */}\n      <main className=\"flex-grow\">\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n          <p className=\"text-center text-gray-500 text-sm\">\n            &copy; {new Date().getFullYear()} EcommerceApp. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AALA;;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,mBAAmB,4KAAA,CAAA,cAAW,CAAC,eAAe;QAE9C,sBAAsB;QACtB,iBAAiB,4KAAA,CAAA,cAAW,CAAC,YAAY;QAEzC,sCAAsC;QACtC,MAAM,sBAAsB;YAC1B,iBAAiB,4KAAA,CAAA,cAAW,CAAC,YAAY;QAC3C;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,4KAAA,CAAA,cAAW,CAAC,MAAM;QACxB,mBAAmB;QACnB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uJAAA,CAAA,OAAI;gDAAC,IAAG;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAI9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAKL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,uJAAA,CAAA,OAAI;4CAAC,IAAG;4CAAQ,WAAU;;8DACzB,8OAAC,+NAAA,CAAA,mBAAgB;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDACjD,gBAAgB,mBACf,8OAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;wCAIN,gCACC;;8DACE,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DAEV,cAAA,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;8DAE5C,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;yEAKH;;8DACE,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,2BACC,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1D,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;;wCAC9B;wCACQ;wCAAc;;;;;;;gCAEtB,gCACC;;sDACE,8OAAC,uJAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,8OAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;iEAKH;;sDACE,8OAAC,uJAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,8OAAC,uJAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAoC;4BACvC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Product, CartService } from '../services';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const handleAddToCart = () => {\n    CartService.addToCart(product, 1);\n    // Trigger storage event to update cart count in Layout\n    window.dispatchEvent(new Event('storage'));\n  };\n\n  return (\n    <div className=\"group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden\">\n      <div className=\"aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 h-48\">\n        {product.image ? (\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"flex flex-col space-y-2 p-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">\n          <Link href={`/products/${product.slug}`} className=\"block\">\n            <span aria-hidden=\"true\" className=\"absolute inset-0\" />\n            {product.name}\n          </Link>\n        </h3>\n        <p className=\"text-sm text-gray-500 line-clamp-2\">{product.description}</p>\n        <div className=\"flex justify-between items-center\">\n          <p className=\"text-lg font-medium text-gray-900\">${Number(product.price).toFixed(2)}</p>\n          {product.quantity > 0 ? (\n            <span className=\"text-sm text-green-600\">In Stock</span>\n          ) : (\n            <span className=\"text-sm text-red-600\">Out of Stock</span>\n          )}\n        </div>\n        <button\n          onClick={handleAddToCart}\n          disabled={product.quantity <= 0}\n          className={`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${\n            product.quantity > 0\n              ? 'bg-indigo-600 hover:bg-indigo-700'\n              : 'bg-gray-400 cursor-not-allowed'\n          }`}\n        >\n          Add to Cart\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAMA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,MAAM,kBAAkB;QACtB,4KAAA,CAAA,cAAW,CAAC,SAAS,CAAC,SAAS;QAC/B,uDAAuD;QACvD,OAAO,aAAa,CAAC,IAAI,MAAM;IACjC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,KAAK,iBACZ,8OAAC;oBACC,KAAK,QAAQ,KAAK;oBAClB,KAAK,QAAQ,IAAI;oBACjB,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;4BAAE,WAAU;;8CACjD,8OAAC;oCAAK,eAAY;oCAAO,WAAU;;;;;;gCAClC,QAAQ,IAAI;;;;;;;;;;;;kCAGjB,8OAAC;wBAAE,WAAU;kCAAsC,QAAQ,WAAW;;;;;;kCACtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAoC;oCAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC;;;;;;;4BAChF,QAAQ,QAAQ,GAAG,kBAClB,8OAAC;gCAAK,WAAU;0CAAyB;;;;;qDAEzC,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAG3C,8OAAC;wBACC,SAAS;wBACT,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,WAAW,CAAC,kIAAkI,EAC5I,QAAQ,QAAQ,GAAG,IACf,sCACA,kCACJ;kCACH;;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Category } from '../services';\n\ninterface CategoryCardProps {\n  category: Category;\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link\n      href={`/categories/${category.slug}`}\n      className=\"group block bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\"\n    >\n      <div className=\"aspect-w-3 aspect-h-2 bg-gray-200 group-hover:opacity-75 h-40\">\n        {category.image ? (\n          <img\n            src={category.image}\n            alt={category.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{category.name}</h3>\n        {category.description && (\n          <p className=\"mt-1 text-sm text-gray-500 line-clamp-2\">{category.description}</p>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;QACpC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,iBACb,8OAAC;oBACC,KAAK,SAAS,KAAK;oBACnB,KAAK,SAAS,IAAI;oBAClB,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,SAAS,IAAI;;;;;;oBAC/D,SAAS,WAAW,kBACnB,8OAAC;wBAAE,WAAU;kCAA2C,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAKtF;uCAEe", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/StripePaymentForm.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useStripe, useElements, CardElement } from \"@stripe/react-stripe-js\";\nimport { StripeCardElementOptions } from \"@stripe/stripe-js\";\n\ninterface StripePaymentFormProps {\n  onSuccess: (paymentMethodId: string) => void;\n  onError: (error: string) => void;\n  loading: boolean;\n  setLoading: (loading: boolean) => void;\n}\n\nconst cardElementOptions: StripeCardElementOptions = {\n  style: {\n    base: {\n      fontSize: \"16px\",\n      color: \"#424770\",\n      \"::placeholder\": {\n        color: \"#aab7c4\",\n      },\n    },\n    invalid: {\n      color: \"#9e2146\",\n    },\n  },\n};\n\nconst StripePaymentForm: React.FC<StripePaymentFormProps> = ({\n  onSuccess,\n  onError,\n  loading,\n  setLoading,\n}) => {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [cardError, setCardError] = useState<string | null>(null);\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    const cardElement = elements.getElement(CardElement);\n\n    if (!cardElement) {\n      return;\n    }\n\n    setLoading(true);\n    setCardError(null);\n\n    try {\n      // Create payment method\n      const { error, paymentMethod } = await stripe.createPaymentMethod({\n        type: \"card\",\n        card: cardElement,\n      });\n\n      if (error) {\n        setCardError(error.message || \"An error occurred during payment\");\n        onError(error.message || \"An error occurred during payment\");\n      } else if (paymentMethod) {\n        onSuccess(paymentMethod.id);\n      }\n    } catch {\n      setCardError(\"Payment failed. Please try again.\");\n      onError(\"Payment failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCardChange = (event: { error?: { message: string } }) => {\n    if (event.error) {\n      setCardError(event.error.message);\n    } else {\n      setCardError(null);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Card Information\n        </label>\n        <div className=\"border border-gray-300 rounded-md p-3 bg-white\">\n          <CardElement\n            options={cardElementOptions}\n            onChange={handleCardChange}\n          />\n        </div>\n        {cardError && <p className=\"mt-1 text-sm text-red-600\">{cardError}</p>}\n      </div>\n\n      <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-blue-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-blue-700\">\n              This is a test environment. Use test card number 4242 4242 4242\n              4242 with any future expiry date and any 3-digit CVC.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={!stripe || loading}\n        className={`w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${\n          !stripe || loading\n            ? \"bg-gray-400 cursor-not-allowed\"\n            : \"bg-indigo-600 hover:bg-indigo-700 cursor-pointer\"\n        }`}\n      >\n        {loading ? (\n          <>\n            <div className=\"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2\"></div>\n            Processing Payment...\n          </>\n        ) : (\n          \"Pay Now\"\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default StripePaymentForm;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,qBAA+C;IACnD,OAAO;QACL,MAAM;YACJ,UAAU;YACV,OAAO;YACP,iBAAiB;gBACf,OAAO;YACT;QACF;QACA,SAAS;YACP,OAAO;QACT;IACF;AACF;AAEA,MAAM,oBAAsD,CAAC,EAC3D,SAAS,EACT,OAAO,EACP,OAAO,EACP,UAAU,EACX;IACC,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,cAAc,SAAS,UAAU,CAAC,oLAAA,CAAA,cAAW;QAEnD,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,wBAAwB;YACxB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,OAAO,mBAAmB,CAAC;gBAChE,MAAM;gBACN,MAAM;YACR;YAEA,IAAI,OAAO;gBACT,aAAa,MAAM,OAAO,IAAI;gBAC9B,QAAQ,MAAM,OAAO,IAAI;YAC3B,OAAO,IAAI,eAAe;gBACxB,UAAU,cAAc,EAAE;YAC5B;QACF,EAAE,OAAM;YACN,aAAa;YACb,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,KAAK,EAAE;YACf,aAAa,MAAM,KAAK,CAAC,OAAO;QAClC,OAAO;YACL,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oLAAA,CAAA,cAAW;4BACV,SAAS;4BACT,UAAU;;;;;;;;;;;oBAGb,2BAAa,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEL,cAAA,8OAAC;oCACC,UAAS;oCACT,GAAE;oCACF,UAAS;;;;;;;;;;;;;;;;sCAIf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU;gBACrB,WAAW,CAAC,+HAA+H,EACzI,CAAC,UAAU,UACP,mCACA,oDACJ;0BAED,wBACC;;sCACE,8OAAC;4BAAI,WAAU;;;;;;wBAAkF;;mCAInG;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n  color?: \"primary\" | \"secondary\" | \"white\";\n  text?: string;\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = \"md\",\n  color = \"primary\",\n  text,\n  className = \"\",\n}) => {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-8 w-8\",\n    lg: \"h-12 w-12\",\n    xl: \"h-16 w-16\",\n  };\n\n  const colorClasses = {\n    primary: \"border-indigo-500\",\n    secondary: \"border-gray-500\",\n    white: \"border-white\",\n  };\n\n  const textSizeClasses = {\n    sm: \"text-sm\",\n    md: \"text-base\",\n    lg: \"text-lg\",\n    xl: \"text-xl\",\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <div\n        className={`animate-spin rounded-full border-t-2 border-b-2 ${sizeClasses[size]} ${colorClasses[color]}`}\n      />\n      {text && (\n        <p className={`mt-2 text-gray-600 ${textSizeClasses[size]}`}>{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,iBAAgD,CAAC,EACrD,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,IAAI,EACJ,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,8OAAC;gBACC,WAAW,CAAC,gDAAgD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,EAAE;;;;;;YAEzG,sBACC,8OAAC;gBAAE,WAAW,CAAC,mBAAmB,EAAE,eAAe,CAAC,KAAK,EAAE;0BAAG;;;;;;;;;;;;AAItE;uCAEe", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/SkeletonLoader.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface SkeletonLineProps {\n  width?: string;\n  height?: string;\n  className?: string;\n}\n\ninterface SkeletonTextProps {\n  lines?: number;\n  className?: string;\n}\n\ninterface SkeletonImageProps {\n  width?: string;\n  height?: string;\n  className?: string;\n}\n\ninterface SkeletonCardProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\ninterface SkeletonGridProps {\n  columns?: number;\n  rows?: number;\n  gap?: string;\n  className?: string;\n}\n\n// Base skeleton animation class\nconst skeletonBase = \"animate-pulse bg-gray-200 rounded\";\n\n// Basic line skeleton component\nexport const SkeletonLine: React.FC<SkeletonLineProps> = ({ \n  width = \"100%\", \n  height = \"1rem\", \n  className = \"\" \n}) => {\n  return (\n    <div \n      className={`${skeletonBase} ${className}`}\n      style={{ width, height }}\n    />\n  );\n};\n\n// Multi-line text skeleton\nexport const SkeletonText: React.FC<SkeletonTextProps> = ({ \n  lines = 3, \n  className = \"\" \n}) => {\n  return (\n    <div className={`space-y-2 ${className}`}>\n      {Array.from({ length: lines }).map((_, index) => (\n        <SkeletonLine \n          key={index}\n          width={index === lines - 1 ? \"75%\" : \"100%\"}\n          height=\"1rem\"\n        />\n      ))}\n    </div>\n  );\n};\n\n// Image placeholder skeleton\nexport const SkeletonImage: React.FC<SkeletonImageProps> = ({ \n  width = \"100%\", \n  height = \"12rem\", \n  className = \"\" \n}) => {\n  return (\n    <div \n      className={`${skeletonBase} flex items-center justify-center ${className}`}\n      style={{ width, height }}\n    >\n      <svg \n        className=\"w-8 h-8 text-gray-300\" \n        fill=\"currentColor\" \n        viewBox=\"0 0 20 20\"\n      >\n        <path \n          fillRule=\"evenodd\" \n          d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" \n          clipRule=\"evenodd\" \n        />\n      </svg>\n    </div>\n  );\n};\n\n// Card container skeleton\nexport const SkeletonCard: React.FC<SkeletonCardProps> = ({ \n  className = \"\", \n  children \n}) => {\n  return (\n    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Grid layout skeleton\nexport const SkeletonGrid: React.FC<SkeletonGridProps> = ({ \n  columns = 4, \n  rows = 3, \n  gap = \"gap-6\", \n  className = \"\" \n}) => {\n  const gridCols = {\n    1: \"grid-cols-1\",\n    2: \"grid-cols-2\", \n    3: \"grid-cols-3\",\n    4: \"grid-cols-4\",\n    5: \"grid-cols-5\",\n    6: \"grid-cols-6\"\n  };\n\n  return (\n    <div className={`grid ${gridCols[columns as keyof typeof gridCols] || 'grid-cols-4'} ${gap} ${className}`}>\n      {Array.from({ length: columns * rows }).map((_, index) => (\n        <SkeletonCard key={index}>\n          <SkeletonImage height=\"8rem\" className=\"mb-4\" />\n          <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n          <SkeletonText lines={2} />\n        </SkeletonCard>\n      ))}\n    </div>\n  );\n};\n\n// Specialized skeleton components for specific use cases\n\n// Product card skeleton\nexport const ProductCardSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <SkeletonCard className={className}>\n      <SkeletonImage height=\"12rem\" className=\"mb-4\" />\n      <SkeletonLine height=\"1.5rem\" className=\"mb-2\" />\n      <SkeletonText lines={2} className=\"mb-3\" />\n      <div className=\"flex justify-between items-center\">\n        <SkeletonLine width=\"4rem\" height=\"1.25rem\" />\n        <SkeletonLine width=\"5rem\" height=\"2rem\" />\n      </div>\n    </SkeletonCard>\n  );\n};\n\n// Category card skeleton\nexport const CategoryCardSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <SkeletonCard className={className}>\n      <SkeletonImage height=\"8rem\" className=\"mb-3\" />\n      <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n      <SkeletonLine width=\"60%\" height=\"1rem\" />\n    </SkeletonCard>\n  );\n};\n\n// Order item skeleton\nexport const OrderItemSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <div className={`flex items-center space-x-4 p-4 border-b border-gray-200 ${className}`}>\n      <SkeletonImage width=\"4rem\" height=\"4rem\" />\n      <div className=\"flex-1\">\n        <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n        <SkeletonLine width=\"40%\" height=\"1rem\" />\n      </div>\n      <div className=\"text-right\">\n        <SkeletonLine width=\"3rem\" height=\"1.25rem\" className=\"mb-1\" />\n        <SkeletonLine width=\"2rem\" height=\"1rem\" />\n      </div>\n    </div>\n  );\n};\n\n// User profile skeleton\nexport const UserProfileSkeleton: React.FC<{ className?: string }> = ({ \n  className = \"\" \n}) => {\n  return (\n    <SkeletonCard className={className}>\n      <div className=\"flex items-center space-x-4 mb-6\">\n        <div className={`${skeletonBase} rounded-full w-16 h-16`} />\n        <div className=\"flex-1\">\n          <SkeletonLine height=\"1.5rem\" className=\"mb-2\" />\n          <SkeletonLine width=\"60%\" height=\"1rem\" />\n        </div>\n      </div>\n      <div className=\"space-y-4\">\n        <div>\n          <SkeletonLine width=\"25%\" height=\"1rem\" className=\"mb-2\" />\n          <SkeletonLine height=\"2.5rem\" />\n        </div>\n        <div>\n          <SkeletonLine width=\"25%\" height=\"1rem\" className=\"mb-2\" />\n          <SkeletonLine height=\"2.5rem\" />\n        </div>\n        <div>\n          <SkeletonLine width=\"25%\" height=\"1rem\" className=\"mb-2\" />\n          <SkeletonLine height=\"2.5rem\" />\n        </div>\n      </div>\n    </SkeletonCard>\n  );\n};\n\n// Table skeleton\nexport const TableSkeleton: React.FC<{ \n  rows?: number; \n  columns?: number; \n  className?: string \n}> = ({ \n  rows = 5, \n  columns = 4, \n  className = \"\" \n}) => {\n  return (\n    <div className={`overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg ${className}`}>\n      <table className=\"min-w-full divide-y divide-gray-300\">\n        <thead className=\"bg-gray-50\">\n          <tr>\n            {Array.from({ length: columns }).map((_, index) => (\n              <th key={index} className=\"px-6 py-3\">\n                <SkeletonLine height=\"1rem\" />\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody className=\"divide-y divide-gray-200 bg-white\">\n          {Array.from({ length: rows }).map((_, rowIndex) => (\n            <tr key={rowIndex}>\n              {Array.from({ length: columns }).map((_, colIndex) => (\n                <td key={colIndex} className=\"px-6 py-4\">\n                  <SkeletonLine height=\"1rem\" />\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n};\n\n// List skeleton\nexport const ListSkeleton: React.FC<{ \n  items?: number; \n  className?: string \n}> = ({ \n  items = 5, \n  className = \"\" \n}) => {\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {Array.from({ length: items }).map((_, index) => (\n        <div key={index} className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\">\n          <SkeletonImage width=\"3rem\" height=\"3rem\" />\n          <div className=\"flex-1\">\n            <SkeletonLine height=\"1.25rem\" className=\"mb-2\" />\n            <SkeletonLine width=\"70%\" height=\"1rem\" />\n          </div>\n          <SkeletonLine width=\"4rem\" height=\"1rem\" />\n        </div>\n      ))}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AA+BA,gCAAgC;AAChC,MAAM,eAAe;AAGd,MAAM,eAA4C,CAAC,EACxD,QAAQ,MAAM,EACd,SAAS,MAAM,EACf,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QACC,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;QACzC,OAAO;YAAE;YAAO;QAAO;;;;;;AAG7B;AAGO,MAAM,eAA4C,CAAC,EACxD,QAAQ,CAAC,EACT,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;kBACrC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAEC,OAAO,UAAU,QAAQ,IAAI,QAAQ;gBACrC,QAAO;eAFF;;;;;;;;;;AAOf;AAGO,MAAM,gBAA8C,CAAC,EAC1D,QAAQ,MAAM,EACd,SAAS,OAAO,EAChB,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QACC,WAAW,GAAG,aAAa,kCAAkC,EAAE,WAAW;QAC1E,OAAO;YAAE;YAAO;QAAO;kBAEvB,cAAA,8OAAC;YACC,WAAU;YACV,MAAK;YACL,SAAQ;sBAER,cAAA,8OAAC;gBACC,UAAS;gBACT,GAAE;gBACF,UAAS;;;;;;;;;;;;;;;;AAKnB;AAGO,MAAM,eAA4C,CAAC,EACxD,YAAY,EAAE,EACd,QAAQ,EACT;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;kBAC7D;;;;;;AAGP;AAGO,MAAM,eAA4C,CAAC,EACxD,UAAU,CAAC,EACX,OAAO,CAAC,EACR,MAAM,OAAO,EACb,YAAY,EAAE,EACf;IACC,MAAM,WAAW;QACf,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,QAAiC,IAAI,cAAc,CAAC,EAAE,IAAI,CAAC,EAAE,WAAW;kBACtG,MAAM,IAAI,CAAC;YAAE,QAAQ,UAAU;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC9C,8OAAC;;kCACC,8OAAC;wBAAc,QAAO;wBAAO,WAAU;;;;;;kCACvC,8OAAC;wBAAa,QAAO;wBAAU,WAAU;;;;;;kCACzC,8OAAC;wBAAa,OAAO;;;;;;;eAHJ;;;;;;;;;;AAQ3B;AAKO,MAAM,sBAAwD,CAAC,EACpE,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAa,WAAW;;0BACvB,8OAAC;gBAAc,QAAO;gBAAQ,WAAU;;;;;;0BACxC,8OAAC;gBAAa,QAAO;gBAAS,WAAU;;;;;;0BACxC,8OAAC;gBAAa,OAAO;gBAAG,WAAU;;;;;;0BAClC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;kCAClC,8OAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;;;;;;;;;;;;;AAI1C;AAGO,MAAM,uBAAyD,CAAC,EACrE,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAa,WAAW;;0BACvB,8OAAC;gBAAc,QAAO;gBAAO,WAAU;;;;;;0BACvC,8OAAC;gBAAa,QAAO;gBAAU,WAAU;;;;;;0BACzC,8OAAC;gBAAa,OAAM;gBAAM,QAAO;;;;;;;;;;;;AAGvC;AAGO,MAAM,oBAAsD,CAAC,EAClE,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;;0BACrF,8OAAC;gBAAc,OAAM;gBAAO,QAAO;;;;;;0BACnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAa,QAAO;wBAAU,WAAU;;;;;;kCACzC,8OAAC;wBAAa,OAAM;wBAAM,QAAO;;;;;;;;;;;;0BAEnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAa,OAAM;wBAAO,QAAO;wBAAU,WAAU;;;;;;kCACtD,8OAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;;;;;;;;;;;;;AAI1C;AAGO,MAAM,sBAAwD,CAAC,EACpE,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAa,WAAW;;0BACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,GAAG,aAAa,uBAAuB,CAAC;;;;;;kCACxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAa,QAAO;gCAAS,WAAU;;;;;;0CACxC,8OAAC;gCAAa,OAAM;gCAAM,QAAO;;;;;;;;;;;;;;;;;;0BAGrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAa,OAAM;gCAAM,QAAO;gCAAO,WAAU;;;;;;0CAClD,8OAAC;gCAAa,QAAO;;;;;;;;;;;;kCAEvB,8OAAC;;0CACC,8OAAC;gCAAa,OAAM;gCAAM,QAAO;gCAAO,WAAU;;;;;;0CAClD,8OAAC;gCAAa,QAAO;;;;;;;;;;;;kCAEvB,8OAAC;;0CACC,8OAAC;gCAAa,OAAM;gCAAM,QAAO;gCAAO,WAAU;;;;;;0CAClD,8OAAC;gCAAa,QAAO;;;;;;;;;;;;;;;;;;;;;;;;AAK/B;AAGO,MAAM,gBAIR,CAAC,EACJ,OAAO,CAAC,EACR,UAAU,CAAC,EACX,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,sEAAsE,EAAE,WAAW;kBAClG,cAAA,8OAAC;YAAM,WAAU;;8BACf,8OAAC;oBAAM,WAAU;8BACf,cAAA,8OAAC;kCACE,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,sBACvC,8OAAC;gCAAe,WAAU;0CACxB,cAAA,8OAAC;oCAAa,QAAO;;;;;;+BADd;;;;;;;;;;;;;;;8BAMf,8OAAC;oBAAM,WAAU;8BACd,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,8OAAC;sCACE,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,8OAAC;oCAAkB,WAAU;8CAC3B,cAAA,8OAAC;wCAAa,QAAO;;;;;;mCADd;;;;;2BAFJ;;;;;;;;;;;;;;;;;;;;;AAYrB;AAGO,MAAM,eAGR,CAAC,EACJ,QAAQ,CAAC,EACT,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;kBACrC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAAgB,WAAU;;kCACzB,8OAAC;wBAAc,OAAM;wBAAO,QAAO;;;;;;kCACnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAa,QAAO;gCAAU,WAAU;;;;;;0CACzC,8OAAC;gCAAa,OAAM;gCAAM,QAAO;;;;;;;;;;;;kCAEnC,8OAAC;wBAAa,OAAM;wBAAO,QAAO;;;;;;;eAN1B;;;;;;;;;;AAWlB", "debugId": null}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/index.ts"], "sourcesContent": ["export { default as Layout } from './Layout';\nexport { default as ProductCard } from './ProductCard';\nexport { default as CategoryCard } from './CategoryCard';\nexport { default as VerificationNotice } from './VerificationNotice';\nexport { default as CsrfToken } from './CsrfToken';\nexport { default as StripePaymentForm } from './StripePaymentForm';\nexport { default as LoadingSpinner } from './LoadingSpinner';\n\n// Skeleton loading components\nexport {\n  SkeletonLine,\n  SkeletonText,\n  SkeletonImage,\n  SkeletonCard,\n  SkeletonGrid,\n  ProductCardSkeleton,\n  CategoryCardSkeleton,\n  OrderItemSkeleton,\n  UserProfileSkeleton,\n  TableSkeleton,\n  ListSkeleton,\n} from './SkeletonLoader';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,8BAA8B;AAC9B", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/categories/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { CategoryService, Category } from \"../../services\";\r\nimport { CategoryCard } from \"../../components\";\r\n\r\nexport default function CategoriesPage() {\r\n  const [categories, setCategories] = useState<Category[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const data = await CategoryService.getAllCategories();\r\n\r\n        // Safely access the data array\r\n        if (data && data.data) {\r\n          setCategories(Array.isArray(data.data) ? data.data : []);\r\n        } else {\r\n          setCategories([]);\r\n        }\r\n\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching categories:\", err);\r\n        setError(\"Failed to load categories. Please try again later.\");\r\n        setCategories([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"bg-red-50 border-l-4 border-red-400 p-4 my-4\">\r\n        <div className=\"flex\">\r\n          <div className=\"flex-shrink-0\">\r\n            <svg\r\n              className=\"h-5 w-5 text-red-400\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"ml-3\">\r\n            <p className=\"text-sm text-red-700\">{error}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">All Categories</h1>\r\n\r\n      {categories.length > 0 ? (\r\n        <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3\">\r\n          {categories.map((category) => (\r\n            <CategoryCard key={category.id} category={category} />\r\n          ))}\r\n        </div>\r\n      ) : (\r\n        <p className=\"text-gray-500\">No categories available.</p>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,WAAW;gBACX,MAAM,OAAO,MAAM,oLAAA,CAAA,kBAAe,CAAC,gBAAgB;gBAEnD,+BAA+B;gBAC/B,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,cAAc,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;gBACzD,OAAO;oBACL,cAAc,EAAE;gBAClB;gBAEA,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,SAAS;gBACT,cAAc,EAAE;YAClB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;YAErD,WAAW,MAAM,GAAG,kBACnB,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,6KAAA,CAAA,eAAY;wBAAmB,UAAU;uBAAvB,SAAS,EAAE;;;;;;;;;qCAIlC,8OAAC;gBAAE,WAAU;0BAAgB;;;;;;;;;;;;AAIrC", "debugId": null}}]}